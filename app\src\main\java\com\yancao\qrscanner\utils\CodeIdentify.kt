package com.yancao.qrscanner.utils

import android.os.Build
import androidx.annotation.RequiresApi
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.*
import io.ktor.client.request.headers
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.*
import java.security.MessageDigest
import java.util.*

/**输入账号密码，回调函数（默认可输入账号61541，密码Jbcj@1#2，回调函数自己定义）
 *初始化先运行CodeIdentify.tokenGetter()获取token
 * 查大条使用CodeIdentify.codeClassfy(code)，返回int，0是正常（绿框），1是盒条未关联（红框），2是条件已关联（黑框）
 * 需要runBlocking来支持协程
 */
class CodeIdentify(
    private val loginUsername: String,
    private val loginPassword: String,
    private val statusCallback: (String) -> Unit
) {
    private var token: String? = null


    @RequiresApi(Build.VERSION_CODES.O)
    suspend fun tokenGetter() {
        val loginUrl = "http://*************/zero-box/mailList/login"

        // Step 1: 构造 tokend 字符串
        val user = loginUsername.reversed()
        val md5Hash = md5(loginPassword)  // 需要 MD5 工具函数
        val reversedMd5 = md5Hash.reversed()
        val tokend = user + "zeroMobile" + reversedMd5

        // Step 2: 生成 a_token (double base64 encode)
        val encodedOnce = String(java.util.Base64.getEncoder().encode(tokend.toByteArray()))
        val encodedTwice = String(java.util.Base64.getEncoder().encode(encodedOnce.toByteArray()))
        val aToken = encodedTwice

        // Step 3: 生成 captcha
        val captcha = UUID.randomUUID().toString()

        // Step 4: 构造 headers
        val headers = headersOf(
            "accept" to listOf("application/json", "text/plain", "*/*"),
            "accept-encoding" to listOf("gzip", "deflate"),
            "accept-language" to listOf("zh-CN","zh;q=0.9","en;q=0.8"),
            "content-type" to listOf("application/json;charset=UTF-8"),
            "origin" to listOf("http://*************"),
            "referer" to listOf("http://*************/factorylevelqr/?singlePoint=true"),
            "host" to listOf("*************"),
            "resource-id" to listOf("3000615"),
            "user-agent" to listOf("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"),
            "a_token" to listOf(aToken),
            "captcha" to listOf(captcha)
        )

        // Step 5: 构造请求体
        val payload = mapOf(
            "device_id" to "9f5ce583516f9aeba7deb8e46f137ecf",
            "platform" to "应用平台",
            "platform_type" to "PC",
            "type" to "more",
            "company_id" to null,
            "verCode" to ""
        )

        // Step 6: 创建 HttpClient 并发送请求
        val client = HttpClient(CIO) {
            install(ContentNegotiation) {
                json()
            }
            expectSuccess = false // 手动处理状态码
        }

        try {
            val response: HttpResponse = client.post(loginUrl) {
                headers{appendAll(headers)}
                setBody(payload)
            }

            // Step 7: 处理响应
            val responseBodyText = response.bodyAsText()

            if (response.status == HttpStatusCode.OK) {
                val tokenFromHeader = response.headers["Token"]
                val tokenFromBody = try {
                    parseTokenFromJson(responseBodyText)
                } catch (e: Exception) {
                    null
                }

                token = tokenFromHeader ?: tokenFromBody

                statusCallback("✅ 登录成功，Token: ${token}")
            } else {
                statusCallback("❌ 登录失败，状态码: ${response.status.value}")
                statusCallback("响应内容: $responseBodyText")
            }
        } catch (e: Exception) {
            statusCallback("❌ 网络错误: ${e.message}")
        } finally {
            client.close()
        }
    }

    // 工具函数：MD5 哈希
    private fun md5(input: String): String {
        return MessageDigest.getInstance("MD5")
            .digest(input.toByteArray())
            .joinToHex()
    }

    private fun ByteArray.joinToHex(): String {
        return joinToString("") { "%02x".format(it) }
    }

    // 简单解析 JSON 中的 token（可用 kotlinx.serialization 替代）
    private fun parseTokenFromJson(json: String): String? {
        return """["']token["']\s*:\s*["']([^"']+)["']""".toRegex()
            .find(json)
            ?.groupValues?.get(1)
    }

    suspend fun codeClassfy(code: String): Int{
        var codestatus = 0

        val headers=headersOf(
            "accept" to listOf("application/json", "text/plain", "*/*"),
            "referer" to listOf("http://*************/factorylevelqr/?singlePoint=true"),
            "resource-id" to listOf("3000703"),
            "user-agent" to listOf("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"),
            "token" to listOf("$token"),
        )
        val quary_url = "http://*************/fm-qrcode-aggregate-data/qrcode/new?qrCode=${code}&packageType=2"

        val client = HttpClient(CIO) {
            install(ContentNegotiation) {
                json()
            }
            expectSuccess = false // 手动处理状态码
        }
        try {
            val response: HttpResponse = client.get(quary_url) {
                headers{appendAll(headers)}
            }

            val jsonString = response.bodyAsText()
            val json = Json.decodeFromString<JsonObject>(jsonString)
            val json_data = json["data"]?.jsonObject
            val brandNameStr = json_data?.get("brandName").toString()
            val pieceCodeStr = json_data?.get("pieceCode").toString()

            codestatus = when{
                brandNameStr == "null" -> 1
                pieceCodeStr == "null" -> 0
                else -> 2
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return codestatus
    }
}