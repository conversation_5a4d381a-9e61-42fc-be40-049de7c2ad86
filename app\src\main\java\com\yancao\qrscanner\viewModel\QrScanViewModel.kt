package com.yancao.qrscanner.viewModel

import android.app.Application
import androidx.annotation.OptIn
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.view.PreviewView
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.mlkit.vision.barcode.common.Barcode
import com.yancao.qrscanner.camera.CameraManager
import com.yancao.qrscanner.domain.ScanResultsHolder

/**
 * 二维码扫描的 ViewModel
 * 负责管理摄像头操作、扫描状态和结果数据
 *
 * 功能说明：
 * - 管理摄像头的启动和停止
 * - 控制实时扫描的开启和关闭
 * - 处理拍照功能
 * - 管理扫描结果的存储和获取
 * - 提供二维码位置信息用于绘制绿框
 * - 新增：集成闪光灯和缩放控制功能
 */
@ExperimentalGetImage
class QrScanViewModel(application: Application) : AndroidViewModel(application) {

    private val cameraManager = CameraManager(application)

    // 实时扫描结果的 LiveData
    val realtimeScanResults = MutableLiveData<List<String>>()

    // 扫描状态的 LiveData
    val isScanningEnabled = MutableLiveData(false)

    // Pair 的第一个元素是检测到的二维码列表，第二个元素是图像尺寸 (width, height)
    val qrCodePositions = MutableLiveData<Pair<List<Barcode>, Pair<Int, Int>>>()

    // 新增：相机控制相关的 LiveData
    val isFlashlightEnabled = MutableLiveData(false)
    val zoomRatio = MutableLiveData(1.0f)
    val minZoomRatio = MutableLiveData(1.0f)
    val maxZoomRatio = MutableLiveData(1.0f)
    val hasFlashlight = MutableLiveData(false)

    // 曝光补偿相关状态
    private val _exposureCompensation = MutableLiveData<Int>()
    val exposureCompensation: LiveData<Int> = _exposureCompensation

    private val _minExposureCompensation = MutableLiveData<Int>()
    val minExposureCompensation: LiveData<Int> = _minExposureCompensation

    private val _maxExposureCompensation = MutableLiveData<Int>()
    val maxExposureCompensation: LiveData<Int> = _maxExposureCompensation

    private val _exposureCompensationStep = MutableLiveData<Float>()

    /**
     * 启动相机
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     * @param enableRealtimeScanning 是否启用实时扫描，默认为 false
     */
    @ExperimentalGetImage
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false
    ) {
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = enableRealtimeScanning,
            onQRCodeDetected = { detectedCodes ->
                // 实时扫描回调，更新 LiveData
                realtimeScanResults.postValue(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                // 位置信息回调，用于绘制绿框
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )

        // 新增：初始化相机控制相关状态
        initializeCameraControlStates()
    }

    /**
     * 初始化相机控制状态
     */
    private fun initializeCameraControlStates() {
        // 检查闪光灯支持
        hasFlashlight.postValue(cameraManager.hasFlashlight())

        // 初始化缩放范围
        minZoomRatio.postValue(cameraManager.getMinZoomRatio())
        maxZoomRatio.postValue(cameraManager.getMaxZoomRatio())
        zoomRatio.postValue(cameraManager.getCurrentZoomRatio())

        // 初始化曝光补偿范围
        _minExposureCompensation.postValue(cameraManager.getMinExposureCompensation())
        _maxExposureCompensation.postValue(cameraManager.getMaxExposureCompensation())
        _exposureCompensation.postValue(cameraManager.getCurrentExposureCompensation())
        _exposureCompensationStep.postValue(cameraManager.getExposureCompensationStep())
    }

    /**
     * 开始实时扫描
     * 这个方法会启用摄像头的实时二维码扫描功能
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     */
    @ExperimentalGetImage
    fun startRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(true)

        // 重新启动摄像头，这次启用实时扫描
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = true,
            onQRCodeDetected = { detectedCodes ->
                // 更新扫描结果
                realtimeScanResults.postValue(detectedCodes)
            },
            onQRCodeWithPosition = { barcodes, imageWidth, imageHeight ->
                // 更新位置信息，用于绘制绿框
                qrCodePositions.postValue(Pair(barcodes, Pair(imageWidth, imageHeight)))
            }
        )

        // 重新初始化相机控制状态
        initializeCameraControlStates()
    }

    /**
     * 停止实时扫描
     * @param previewView 预览组件
     * @param lifecycleOwner 生命周期拥有者
     */
    @ExperimentalGetImage
    fun stopRealtimeScanning(previewView: PreviewView, lifecycleOwner: LifecycleOwner) {
        isScanningEnabled.postValue(false)

        // 重新启动摄像头，这次禁用实时扫描
        cameraManager.startCamera(
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            enableRealtimeScanning = false,
            onQRCodeDetected = null,
            onQRCodeWithPosition = null
        )

        // 重新初始化相机控制状态
        initializeCameraControlStates()
    }

    // 新增：闪光灯控制方法
    /**
     * 切换闪光灯状态
     * @return 操作是否成功
     */
    fun toggleFlashlight(): Boolean {
        val currentState = isFlashlightEnabled.value ?: false
        val newState = !currentState

        return if (cameraManager.setFlashlight(newState)) {
            isFlashlightEnabled.postValue(newState)
            true
        } else {
            false
        }
    }


    //缩放控制方法
    /**
     * 设置缩放比例
     * @param ratio 缩放比例
     * @return 操作是否成功
     */
    fun setZoomRatio(ratio: Float): Boolean {
        return if (cameraManager.setZoomRatio(ratio)) {
            zoomRatio.postValue(ratio)
            true
        } else {
            false
        }
    }


    /**
     * 获取当前存储的所有扫描结果
     * @return 扫描结果列表
     */
    fun getAllScanResults(): List<String> {
        return ScanResultsHolder.scanResults
    }

    /**
     * 设置曝光补偿
     * @param index 曝光补偿索引
     * @return 操作是否成功
     */
    fun setExposureCompensation(index: Int): Boolean {
        return if (cameraManager.setExposureCompensation(index)) {
            _exposureCompensation.postValue(index)
            true
        } else {
            false
        }
    }

    /**
     * 增加曝光补偿（使图像更亮）
     * @param step 步长，默认1
     */
    fun increaseExposure(step: Int = 1) {
        val currentIndex = exposureCompensation.value ?: 0
        val maxIndex = maxExposureCompensation.value ?: 2
        val newIndex = (currentIndex + step).coerceAtMost(maxIndex)
        setExposureCompensation(newIndex)
    }

    /**
     * 减少曝光补偿（使图像更暗）
     * @param step 步长，默认1
     */
    fun decreaseExposure(step: Int = 1) {
        val currentIndex = exposureCompensation.value ?: 0
        val minIndex = minExposureCompensation.value ?: -2
        val newIndex = (currentIndex - step).coerceAtLeast(minIndex)
        setExposureCompensation(newIndex)
    }


    /**
     * ViewModel 清理时的操作
     * 释放摄像头资源，防止内存泄漏
     */
    @OptIn(ExperimentalGetImage::class)
    override fun onCleared() {
        super.onCleared()
        cameraManager.shutdown()
    }
}